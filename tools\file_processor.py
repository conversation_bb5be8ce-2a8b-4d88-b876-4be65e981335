"""File processing tool for handling PDF and TXT documents."""

import os
import pickle
from typing import List, Optional
from pathlib import Path

import PyPDF2
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.schema import Document

from config import Config


class FileProcessor:
    """Tool for processing and indexing local files."""
    
    def __init__(self):
        """Initialize the file processor."""
        self.config = Config()
        self.embeddings = HuggingFaceEmbeddings(
            model_name=self.config.EMBEDDING_MODEL_NAME
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.CHUNK_SIZE,
            chunk_overlap=self.config.CHUNK_OVERLAP,
            length_function=len,
        )
        
        # Ensure directories exist
        os.makedirs(self.config.VECTOR_STORE_PATH, exist_ok=True)
        os.makedirs(self.config.DOCUMENTS_PATH, exist_ok=True)
    
    def process_file(self, file_path: str) -> str:
        """
        Process a file and create a searchable vector store.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            Status message about the processing
        """
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                return f"Error: File '{file_path}' not found."
            
            # Get file extension
            file_ext = Path(file_path).suffix.lower()
            
            # Extract text based on file type
            if file_ext == '.pdf':
                text = self._extract_pdf_text(file_path)
            elif file_ext == '.txt':
                text = self._extract_txt_text(file_path)
            else:
                return f"Error: Unsupported file type '{file_ext}'. Supported types: .pdf, .txt"
            
            if not text.strip():
                return "Error: No text content found in the file."
            
            # Create document chunks
            documents = self._create_documents(text, file_path)
            
            # Create or update vector store
            vector_store_path = self._get_vector_store_path(file_path)
            vector_store = self._create_vector_store(documents, vector_store_path)
            
            return f"Successfully processed '{file_path}'. Created {len(documents)} text chunks and indexed them for searching."
            
        except Exception as e:
            return f"Error processing file: {str(e)}"
    
    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from a PDF file."""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            raise Exception(f"Failed to extract text from PDF: {str(e)}")
        
        return text
    
    def _extract_txt_text(self, file_path: str) -> str:
        """Extract text from a TXT file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    return file.read()
            except Exception as e:
                raise Exception(f"Failed to read text file: {str(e)}")
        except Exception as e:
            raise Exception(f"Failed to read text file: {str(e)}")
    
    def _create_documents(self, text: str, file_path: str) -> List[Document]:
        """Create document chunks from text."""
        # Split text into chunks
        chunks = self.text_splitter.split_text(text)
        
        # Create Document objects with metadata
        documents = []
        for i, chunk in enumerate(chunks):
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": file_path,
                    "chunk_id": i,
                    "total_chunks": len(chunks)
                }
            )
            documents.append(doc)
        
        return documents
    
    def _get_vector_store_path(self, file_path: str) -> str:
        """Generate vector store path for a file."""
        file_name = Path(file_path).stem
        return os.path.join(self.config.VECTOR_STORE_PATH, f"{file_name}_vectorstore")
    
    def _create_vector_store(self, documents: List[Document], vector_store_path: str) -> FAISS:
        """Create and save a FAISS vector store."""
        # Create vector store
        vector_store = FAISS.from_documents(documents, self.embeddings)
        
        # Save vector store
        vector_store.save_local(vector_store_path)
        
        # Also save metadata
        metadata_path = f"{vector_store_path}_metadata.pkl"
        metadata = {
            "num_documents": len(documents),
            "sources": list(set([doc.metadata["source"] for doc in documents]))
        }
        with open(metadata_path, 'wb') as f:
            pickle.dump(metadata, f)
        
        return vector_store
    
    def list_processed_files(self) -> List[str]:
        """List all processed files."""
        processed_files = []
        if os.path.exists(self.config.VECTOR_STORE_PATH):
            for item in os.listdir(self.config.VECTOR_STORE_PATH):
                if item.endswith("_metadata.pkl"):
                    metadata_path = os.path.join(self.config.VECTOR_STORE_PATH, item)
                    try:
                        with open(metadata_path, 'rb') as f:
                            metadata = pickle.load(f)
                            processed_files.extend(metadata.get("sources", []))
                    except:
                        continue
        return processed_files
