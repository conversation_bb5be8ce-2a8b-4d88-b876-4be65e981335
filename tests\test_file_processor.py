"""Tests for the FileProcessor tool."""

import os
import tempfile
import pytest
from pathlib import Path

from tools.file_processor import FileProcessor


class TestFileProcessor:
    """Test cases for FileProcessor."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = FileProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_process_txt_file(self):
        """Test processing a TXT file."""
        # Create a test TXT file
        test_content = "This is a test document.\nIt contains multiple lines.\nFor testing purposes."
        test_file = os.path.join(self.temp_dir, "test.txt")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Process the file
        result = self.processor.process_file(test_file)
        
        # Check result
        assert "Successfully processed" in result
        assert "test.txt" in result
        assert "chunks" in result
    
    def test_process_nonexistent_file(self):
        """Test processing a non-existent file."""
        result = self.processor.process_file("nonexistent.txt")
        assert "Error: File" in result
        assert "not found" in result
    
    def test_process_unsupported_file(self):
        """Test processing an unsupported file type."""
        # Create a test file with unsupported extension
        test_file = os.path.join(self.temp_dir, "test.docx")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        result = self.processor.process_file(test_file)
        assert "Error: Unsupported file type" in result
    
    def test_extract_txt_text(self):
        """Test text extraction from TXT file."""
        test_content = "Hello, world!\nThis is a test."
        test_file = os.path.join(self.temp_dir, "test.txt")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        extracted_text = self.processor._extract_txt_text(test_file)
        assert extracted_text == test_content
    
    def test_create_documents(self):
        """Test document creation and chunking."""
        test_text = "This is a test document. " * 100  # Long text to trigger chunking
        test_file = "test.txt"
        
        documents = self.processor._create_documents(test_text, test_file)
        
        assert len(documents) > 0
        assert all(doc.metadata["source"] == test_file for doc in documents)
        assert all("chunk_id" in doc.metadata for doc in documents)
    
    def test_get_vector_store_path(self):
        """Test vector store path generation."""
        test_file = "/path/to/test_document.pdf"
        expected_path = os.path.join(self.processor.config.VECTOR_STORE_PATH, "test_document_vectorstore")
        
        actual_path = self.processor._get_vector_store_path(test_file)
        assert actual_path == expected_path
