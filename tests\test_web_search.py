"""Tests for the WebSearchTool."""

import pytest
from unittest.mock import Mock, patch

from tools.web_search import WebSearchTool


class TestWebSearchTool:
    """Test cases for WebSearchTool."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.search_tool = WebSearchTool(max_results=3)
    
    def test_initialization(self):
        """Test tool initialization."""
        assert self.search_tool.max_results == 3
        assert hasattr(self.search_tool, 'ddgs')
    
    def test_empty_query(self):
        """Test search with empty query."""
        result = self.search_tool.search("")
        assert "Error: Search query cannot be empty" in result
        
        result = self.search_tool.search("   ")
        assert "Error: Search query cannot be empty" in result
    
    @patch('tools.web_search.DDGS')
    def test_successful_search(self, mock_ddgs):
        """Test successful web search."""
        # Mock search results
        mock_results = [
            {
                'title': 'Test Result 1',
                'body': 'This is a test result description.',
                'href': 'https://example.com/1'
            },
            {
                'title': 'Test Result 2',
                'body': 'Another test result description.',
                'href': 'https://example.com/2'
            }
        ]
        
        mock_ddgs_instance = Mock()
        mock_ddgs_instance.text.return_value = mock_results
        mock_ddgs.return_value = mock_ddgs_instance
        
        # Create new instance with mocked DDGS
        search_tool = WebSearchTool()
        search_tool.ddgs = mock_ddgs_instance
        
        result = search_tool.search("test query")
        
        assert "Web search results for: 'test query'" in result
        assert "Test Result 1" in result
        assert "Test Result 2" in result
        assert "https://example.com/1" in result
        assert "Found 2 results" in result
    
    @patch('tools.web_search.DDGS')
    def test_no_results_found(self, mock_ddgs):
        """Test search with no results."""
        mock_ddgs_instance = Mock()
        mock_ddgs_instance.text.return_value = []
        mock_ddgs.return_value = mock_ddgs_instance
        
        search_tool = WebSearchTool()
        search_tool.ddgs = mock_ddgs_instance
        
        result = search_tool.search("nonexistent query")
        
        assert "No search results found for query: 'nonexistent query'" in result
    
    @patch('tools.web_search.DDGS')
    def test_search_exception(self, mock_ddgs):
        """Test search with exception."""
        mock_ddgs_instance = Mock()
        mock_ddgs_instance.text.side_effect = Exception("Network error")
        mock_ddgs.return_value = mock_ddgs_instance
        
        search_tool = WebSearchTool()
        search_tool.ddgs = mock_ddgs_instance
        
        result = search_tool.search("test query")
        
        assert "Error performing web search" in result
        assert "Network error" in result
    
    def test_format_search_results(self):
        """Test search results formatting."""
        query = "test query"
        results = [
            {
                'title': 'Test Title',
                'body': 'Test description',
                'href': 'https://example.com'
            }
        ]
        
        formatted = self.search_tool._format_search_results(query, results)
        
        assert "Web search results for: 'test query'" in formatted
        assert "Test Title" in formatted
        assert "Test description" in formatted
        assert "https://example.com" in formatted
        assert "Found 1 results" in formatted
    
    def test_long_description_truncation(self):
        """Test truncation of long descriptions."""
        query = "test"
        long_description = "A" * 300  # Long description
        results = [
            {
                'title': 'Test',
                'body': long_description,
                'href': 'https://example.com'
            }
        ]
        
        formatted = self.search_tool._format_search_results(query, results)
        
        # Should be truncated to 200 chars + "..."
        assert "A" * 200 + "..." in formatted
        assert len([line for line in formatted.split('\n') if 'A' * 250 in line]) == 0
