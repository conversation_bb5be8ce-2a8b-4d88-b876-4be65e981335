"""Core autonomous agent implementation using <PERSON><PERSON>hain."""

import os
from typing import List, Dict, Any, Optional
from langchain.agents import AgentExecutor, create_react_agent
from langchain.memory import ConversationBufferMemory
from langchain.schema import BaseMessage
from langchain_huggingface import HuggingFaceEndpoint
from langchain.prompts import PromptTemplate
from langchain.tools import Tool
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from config import Config
from tools import FileProcessor, DocumentQARetriever, WebSearchTool


class AutonomousAgent:
    """
    Autonomous task-based agent powered by LLM with document processing,
    web search, and conversational memory capabilities.
    """
    
    def __init__(self):
        """Initialize the autonomous agent."""
        self.console = Console()
        self.config = Config()
        self.config.validate()
        
        # Initialize memory
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # Initialize LLM
        self.llm = self._initialize_llm()
        
        # Initialize tools
        self.tools = self._initialize_tools()
        
        # Initialize agent
        self.agent_executor = self._initialize_agent()
        
        self.console.print(Panel(
            Text("🤖 Autonomous Agent Initialized", style="bold green"),
            title="Agent Status"
        ))
    
    def _initialize_llm(self) -> HuggingFaceEndpoint:
        """Initialize the Hugging Face LLM."""
        return HuggingFaceEndpoint(
            repo_id=self.config.LLM_MODEL_NAME,
            huggingfacehub_api_token=self.config.HUGGINGFACE_API_TOKEN,
            max_new_tokens=self.config.MAX_TOKENS,
            temperature=self.config.TEMPERATURE,
            repetition_penalty=1.1
        )
    
    def _initialize_tools(self) -> List[Tool]:
        """Initialize all available tools."""
        tools = []
        
        # File processor tool
        file_processor = FileProcessor()
        tools.append(Tool(
            name="file_processor",
            description="Process and index local files (PDF, TXT) for later querying. "
                       "Use this when user wants to upload or process a document.",
            func=file_processor.process_file
        ))
        
        # Document QA tool
        doc_qa = DocumentQARetriever()
        tools.append(Tool(
            name="document_qa",
            description="Answer questions based on previously processed documents. "
                       "Use this when user asks questions about uploaded documents.",
            func=doc_qa.query_documents
        ))
        
        # Web search tool
        web_search = WebSearchTool()
        tools.append(Tool(
            name="web_search",
            description="Search the web for information. "
                       "Use this for general knowledge questions or current information.",
            func=web_search.search
        ))
        
        return tools
    
    def _initialize_agent(self) -> AgentExecutor:
        """Initialize the ReAct agent with tools and memory."""
        prompt = PromptTemplate.from_template("""
You are an intelligent autonomous agent designed to help users with various tasks.
You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Previous conversation:
{chat_history}

Question: {input}
{agent_scratchpad}
        """)
        
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5
        )
    
    def chat(self, user_input: str) -> str:
        """
        Process user input and return agent response.
        
        Args:
            user_input: User's question or command
            
        Returns:
            Agent's response
        """
        try:
            self.console.print(Panel(
                Text(f"User: {user_input}", style="bold blue"),
                title="Input"
            ))
            
            # Process the input through the agent
            response = self.agent_executor.invoke({"input": user_input})
            
            # Extract the final answer
            final_answer = response.get("output", "I'm sorry, I couldn't process that request.")
            
            self.console.print(Panel(
                Text(final_answer, style="bold green"),
                title="Agent Response"
            ))
            
            return final_answer
            
        except Exception as e:
            error_msg = f"An error occurred: {str(e)}"
            self.console.print(Panel(
                Text(error_msg, style="bold red"),
                title="Error"
            ))
            return error_msg
    
    def get_conversation_history(self) -> List[BaseMessage]:
        """Get the current conversation history."""
        return self.memory.chat_memory.messages
    
    def clear_memory(self):
        """Clear the conversation memory."""
        self.memory.clear()
        self.console.print(Panel(
            Text("Memory cleared", style="bold yellow"),
            title="Memory Status"
        ))
    
    def list_available_tools(self) -> List[str]:
        """List all available tools."""
        return [tool.name for tool in self.tools]
