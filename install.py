#!/usr/bin/env python3
"""
Installation and setup script for the Autonomous Task-Based Agent.

This script helps users set up the agent with proper dependencies and configuration.
"""

import os
import sys
import subprocess
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm


class AgentInstaller:
    """Installer for the Autonomous Task-Based Agent."""
    
    def __init__(self):
        """Initialize the installer."""
        self.console = Console()
        self.project_root = Path(__file__).parent
    
    def display_welcome(self):
        """Display welcome message."""
        welcome_text = Text()
        welcome_text.append("🤖 Autonomous Task-Based Agent\n", style="bold blue")
        welcome_text.append("Installation and Setup Wizard\n\n", style="italic")
        welcome_text.append("This wizard will help you set up the agent with all required dependencies\n")
        welcome_text.append("and configuration files.", style="dim")
        
        self.console.print(Panel(welcome_text, title="Welcome", border_style="blue"))
    
    def check_python_version(self):
        """Check if Python version is compatible."""
        self.console.print("🔍 Checking Python version...", style="yellow")
        
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            self.console.print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible", style="green")
            return True
        else:
            self.console.print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible", style="red")
            self.console.print("Python 3.8 or higher is required", style="red")
            return False
    
    def install_dependencies(self):
        """Install required dependencies."""
        self.console.print("📦 Installing dependencies...", style="yellow")
        
        try:
            # Install requirements
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.console.print("✅ Dependencies installed successfully", style="green")
                return True
            else:
                self.console.print("❌ Failed to install dependencies", style="red")
                self.console.print(result.stderr, style="red")
                return False
                
        except Exception as e:
            self.console.print(f"❌ Error installing dependencies: {e}", style="red")
            return False
    
    def setup_environment(self):
        """Set up environment configuration."""
        self.console.print("⚙️  Setting up environment configuration...", style="yellow")
        
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if env_file.exists():
            if not Confirm.ask("📄 .env file already exists. Overwrite?"):
                self.console.print("⏭️  Skipping environment setup", style="yellow")
                return True
        
        # Copy example file
        try:
            with open(env_example, 'r') as src:
                content = src.read()
            
            # Get Hugging Face token from user
            self.console.print("\n🔑 Hugging Face API Token Setup", style="bold")
            self.console.print("You need a Hugging Face API token to use the Mixtral model.")
            self.console.print("Get one at: https://huggingface.co/settings/tokens")
            
            token = Prompt.ask("Enter your Hugging Face API token", password=True)
            
            if token:
                content = content.replace("your_huggingface_token_here", token)
            
            with open(env_file, 'w') as dst:
                dst.write(content)
            
            self.console.print("✅ Environment configuration created", style="green")
            return True
            
        except Exception as e:
            self.console.print(f"❌ Error setting up environment: {e}", style="red")
            return False
    
    def create_directories(self):
        """Create necessary directories."""
        self.console.print("📁 Creating directories...", style="yellow")
        
        directories = ["vector_stores", "documents", "examples"]
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(exist_ok=True)
            
            self.console.print("✅ Directories created", style="green")
            return True
            
        except Exception as e:
            self.console.print(f"❌ Error creating directories: {e}", style="red")
            return False
    
    def run_tests(self):
        """Run basic tests to verify installation."""
        if not Confirm.ask("🧪 Run tests to verify installation?"):
            return True
        
        self.console.print("🧪 Running tests...", style="yellow")
        
        try:
            # Test imports
            result = subprocess.run([
                sys.executable, "-c", 
                "from agent import AutonomousAgent; from tools import FileProcessor, DocumentQARetriever, WebSearchTool; print('✅ All imports successful')"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.console.print("✅ Basic tests passed", style="green")
                return True
            else:
                self.console.print("❌ Tests failed", style="red")
                self.console.print(result.stderr, style="red")
                return False
                
        except Exception as e:
            self.console.print(f"❌ Error running tests: {e}", style="red")
            return False
    
    def display_completion(self):
        """Display completion message."""
        completion_text = Text()
        completion_text.append("🎉 Installation Complete!\n\n", style="bold green")
        completion_text.append("Next steps:\n", style="bold")
        completion_text.append("1. Run the agent: python main.py\n")
        completion_text.append("2. Try the examples: python examples/usage_examples.py\n")
        completion_text.append("3. Read the documentation: README.md\n\n")
        completion_text.append("For help, type 'help' in the agent or check the README.", style="dim")
        
        self.console.print(Panel(completion_text, title="Setup Complete", border_style="green"))
    
    def install(self):
        """Run the complete installation process."""
        self.display_welcome()
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Installing dependencies", self.install_dependencies),
            ("Setting up environment", self.setup_environment),
            ("Creating directories", self.create_directories),
            ("Running tests", self.run_tests),
        ]
        
        for step_name, step_func in steps:
            if not step_func():
                self.console.print(f"\n❌ Installation failed at: {step_name}", style="bold red")
                return False
        
        self.display_completion()
        return True


def main():
    """Main entry point."""
    installer = AgentInstaller()
    
    try:
        success = installer.install()
        return 0 if success else 1
    except KeyboardInterrupt:
        installer.console.print("\n\n⚠️  Installation cancelled by user", style="yellow")
        return 1
    except Exception as e:
        installer.console.print(f"\n❌ Unexpected error: {e}", style="red")
        return 1


if __name__ == "__main__":
    sys.exit(main())
