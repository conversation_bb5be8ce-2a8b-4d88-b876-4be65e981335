"""Web search tool using DuckDuckGo for information retrieval."""

from typing import List, Dict, Any, Optional
import json
from duckduckgo_search import DDGS


class WebSearchTool:
    """Tool for searching the web using DuckDuckGo."""
    
    def __init__(self, max_results: int = 5):
        """
        Initialize the web search tool.
        
        Args:
            max_results: Maximum number of search results to return
        """
        self.max_results = max_results
        self.ddgs = DDGS()
    
    def search(self, query: str, max_results: Optional[int] = None) -> str:
        """
        Search the web for information.
        
        Args:
            query: Search query
            max_results: Maximum number of results (overrides default)
            
        Returns:
            Formatted search results
        """
        try:
            if not query.strip():
                return "Error: Search query cannot be empty."
            
            # Use provided max_results or default
            num_results = max_results if max_results is not None else self.max_results
            
            # Perform the search
            results = list(self.ddgs.text(
                keywords=query,
                max_results=num_results,
                region='wt-wt',  # Worldwide
                safesearch='moderate'
            ))
            
            if not results:
                return f"No search results found for query: '{query}'"
            
            # Format the results
            formatted_results = self._format_search_results(query, results)
            
            return formatted_results
            
        except Exception as e:
            return f"Error performing web search: {str(e)}"
    
    def search_news(self, query: str, max_results: Optional[int] = None) -> str:
        """
        Search for news articles.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            Formatted news results
        """
        try:
            if not query.strip():
                return "Error: Search query cannot be empty."
            
            num_results = max_results if max_results is not None else self.max_results
            
            # Perform news search
            results = list(self.ddgs.news(
                keywords=query,
                max_results=num_results,
                region='wt-wt',
                safesearch='moderate'
            ))
            
            if not results:
                return f"No news results found for query: '{query}'"
            
            # Format the results
            formatted_results = self._format_news_results(query, results)
            
            return formatted_results
            
        except Exception as e:
            return f"Error performing news search: {str(e)}"
    
    def _format_search_results(self, query: str, results: List[Dict[str, Any]]) -> str:
        """Format web search results."""
        response_parts = []
        response_parts.append(f"🔍 Web search results for: '{query}'\n")
        
        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            body = result.get('body', 'No description')
            href = result.get('href', 'No URL')
            
            # Truncate long descriptions
            if len(body) > 200:
                body = body[:200] + "..."
            
            response_parts.append(f"\n{i}. **{title}**")
            response_parts.append(f"   {body}")
            response_parts.append(f"   🔗 {href}\n")
        
        response_parts.append(f"\n📊 Found {len(results)} results")
        
        return "".join(response_parts)
    
    def _format_news_results(self, query: str, results: List[Dict[str, Any]]) -> str:
        """Format news search results."""
        response_parts = []
        response_parts.append(f"📰 News results for: '{query}'\n")
        
        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            body = result.get('body', 'No description')
            url = result.get('url', 'No URL')
            date = result.get('date', 'No date')
            source = result.get('source', 'Unknown source')
            
            # Truncate long descriptions
            if len(body) > 200:
                body = body[:200] + "..."
            
            response_parts.append(f"\n{i}. **{title}**")
            response_parts.append(f"   📅 {date} | 📰 {source}")
            response_parts.append(f"   {body}")
            response_parts.append(f"   🔗 {url}\n")
        
        response_parts.append(f"\n📊 Found {len(results)} news articles")
        
        return "".join(response_parts)
    
    def quick_answer(self, question: str) -> str:
        """
        Get a quick answer to a question using web search.
        
        Args:
            question: Question to answer
            
        Returns:
            Quick answer based on search results
        """
        try:
            # Search for the question
            results = list(self.ddgs.text(
                keywords=question,
                max_results=3,
                region='wt-wt',
                safesearch='moderate'
            ))
            
            if not results:
                return f"No information found for: '{question}'"
            
            # Extract the most relevant information
            response_parts = []
            response_parts.append(f"💡 Quick answer for: '{question}'\n")
            
            # Use the first result as the primary answer
            first_result = results[0]
            title = first_result.get('title', '')
            body = first_result.get('body', '')
            
            if body:
                # Take the first sentence or two as a quick answer
                sentences = body.split('. ')
                quick_answer = '. '.join(sentences[:2])
                if len(quick_answer) > 300:
                    quick_answer = quick_answer[:300] + "..."
                
                response_parts.append(f"{quick_answer}\n")
                response_parts.append(f"📖 Source: {title}")
                response_parts.append(f"🔗 {first_result.get('href', '')}")
            else:
                response_parts.append(f"📖 {title}")
                response_parts.append(f"🔗 {first_result.get('href', '')}")
            
            return "".join(response_parts)
            
        except Exception as e:
            return f"Error getting quick answer: {str(e)}"
