"""Configuration settings for the autonomous agent."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the autonomous agent."""
    
    # Hugging Face settings
    HUGGINGFACE_API_TOKEN = os.getenv("HUGGINGFACE_API_TOKEN")
    
    # Model configurations
    LLM_MODEL_NAME = os.getenv("LLM_MODEL_NAME", "mistralai/Mixtral-8x7B-Instruct-v0.1")
    EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2")
    
    # LLM parameters
    MAX_TOKENS = int(os.getenv("MAX_TOKENS", "1000"))
    TEMPERATURE = float(os.getenv("TEMPERATURE", "0.7"))
    
    # Document processing settings
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "200"))
    
    # Storage paths
    VECTOR_STORE_PATH = "vector_stores"
    DOCUMENTS_PATH = "documents"
    
    @classmethod
    def validate(cls):
        """Validate required configuration."""
        if not cls.HUGGINGFACE_API_TOKEN:
            raise ValueError(
                "HUGGINGFACE_API_TOKEN is required. "
                "Please set it in your .env file or environment variables."
            )
        return True
