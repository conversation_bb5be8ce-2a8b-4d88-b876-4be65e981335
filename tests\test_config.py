"""Tests for configuration module."""

import os
import pytest
from unittest.mock import patch

from config import Config


class TestConfig:
    """Test cases for Config class."""
    
    def test_default_values(self):
        """Test default configuration values."""
        assert Config.LLM_MODEL_NAME == "mistralai/Mixtral-8x7B-Instruct-v0.1"
        assert Config.EMBEDDING_MODEL_NAME == "sentence-transformers/all-MiniLM-L6-v2"
        assert Config.MAX_TOKENS == 1000
        assert Config.TEMPERATURE == 0.7
        assert Config.CHUNK_SIZE == 1000
        assert Config.CHUNK_OVERLAP == 200
        assert Config.VECTOR_STORE_PATH == "vector_stores"
        assert Config.DOCUMENTS_PATH == "documents"
    
    @patch.dict(os.environ, {'HUGGINGFACE_API_TOKEN': 'test_token'})
    def test_validation_with_token(self):
        """Test validation with valid token."""
        # Reload config to pick up environment variable
        import importlib
        import config
        importlib.reload(config)
        
        assert config.Config.validate() is True
    
    @patch.dict(os.environ, {}, clear=True)
    def test_validation_without_token(self):
        """Test validation without token."""
        # Reload config to clear environment variable
        import importlib
        import config
        importlib.reload(config)
        
        with pytest.raises(ValueError) as exc_info:
            config.Config.validate()
        
        assert "HUGGINGFACE_API_TOKEN is required" in str(exc_info.value)
    
    @patch.dict(os.environ, {
        'LLM_MODEL_NAME': 'custom/model',
        'MAX_TOKENS': '2000',
        'TEMPERATURE': '0.5'
    })
    def test_environment_override(self):
        """Test environment variable override."""
        # Reload config to pick up environment variables
        import importlib
        import config
        importlib.reload(config)
        
        assert config.Config.LLM_MODEL_NAME == 'custom/model'
        assert config.Config.MAX_TOKENS == 2000
        assert config.Config.TEMPERATURE == 0.5
