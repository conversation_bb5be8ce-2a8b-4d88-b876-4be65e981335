#!/usr/bin/env python3
"""
Usage examples for the Autonomous Task-Based Agent.

This script demonstrates how to use the agent programmatically.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent import AutonomousAgent
from config import Config


def example_document_processing():
    """Example: Process a document and ask questions."""
    print("=== Document Processing Example ===")
    
    # Initialize the agent
    agent = AutonomousAgent()
    
    # Process a document
    print("\n1. Processing document...")
    response = agent.chat("Please process the file: examples/sample_document.txt")
    print(f"Response: {response}")
    
    # Ask questions about the document
    print("\n2. Asking questions about the document...")
    
    questions = [
        "What is the main topic of this document?",
        "What are the key applications of AI mentioned?",
        "What challenges does the document discuss?",
        "Can you summarize the conclusion?"
    ]
    
    for question in questions:
        print(f"\nQuestion: {question}")
        response = agent.chat(question)
        print(f"Answer: {response}")


def example_web_search():
    """Example: Perform web searches."""
    print("\n=== Web Search Example ===")
    
    # Initialize the agent
    agent = AutonomousAgent()
    
    # Perform web searches
    search_queries = [
        "What are the latest developments in artificial intelligence?",
        "Current news about machine learning",
        "Recent breakthroughs in natural language processing"
    ]
    
    for query in search_queries:
        print(f"\nSearch Query: {query}")
        response = agent.chat(query)
        print(f"Results: {response}")


def example_mixed_interaction():
    """Example: Mixed document and web search interaction."""
    print("\n=== Mixed Interaction Example ===")
    
    # Initialize the agent
    agent = AutonomousAgent()
    
    # Process document first
    print("\n1. Processing document...")
    agent.chat("Please process the file: examples/sample_document.txt")
    
    # Mixed queries
    mixed_queries = [
        "Based on the document I uploaded, search for recent news about AI in healthcare",
        "How do current AI developments compare to what's mentioned in my document?",
        "Find recent examples of the AI applications discussed in the document"
    ]
    
    for query in mixed_queries:
        print(f"\nMixed Query: {query}")
        response = agent.chat(query)
        print(f"Response: {response}")


def example_conversation_memory():
    """Example: Demonstrate conversation memory."""
    print("\n=== Conversation Memory Example ===")
    
    # Initialize the agent
    agent = AutonomousAgent()
    
    # Sequential conversation
    conversation = [
        "What is machine learning?",
        "Can you give me more details about the types you mentioned?",
        "How does supervised learning work specifically?",
        "What are some real-world examples of what we just discussed?"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"\nTurn {i}: {message}")
        response = agent.chat(message)
        print(f"Response: {response}")
    
    # Show conversation history
    print("\n--- Conversation History ---")
    history = agent.get_conversation_history()
    for i, msg in enumerate(history):
        role = "User" if msg.type == "human" else "Agent"
        content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
        print(f"{i+1}. {role}: {content}")


def example_tool_usage():
    """Example: Demonstrate explicit tool usage."""
    print("\n=== Tool Usage Example ===")
    
    # Initialize the agent
    agent = AutonomousAgent()
    
    # Show available tools
    print("Available tools:", agent.list_available_tools())
    
    # Use specific tools
    tool_examples = [
        "Use the file processor to process examples/sample_document.txt",
        "Use the web search tool to find information about Python programming",
        "Use the document QA tool to answer: What does the document say about AI ethics?"
    ]
    
    for example in tool_examples:
        print(f"\nTool Example: {example}")
        response = agent.chat(example)
        print(f"Response: {response}")


def main():
    """Run all examples."""
    try:
        # Validate configuration
        Config.validate()
        
        print("🤖 Autonomous Agent Usage Examples")
        print("=" * 50)
        
        # Run examples
        example_document_processing()
        example_web_search()
        example_mixed_interaction()
        example_conversation_memory()
        example_tool_usage()
        
        print("\n✅ All examples completed successfully!")
        
    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please ensure HUGGINGFACE_API_TOKEN is set in your .env file")
    except Exception as e:
        print(f"❌ Error running examples: {e}")


if __name__ == "__main__":
    main()
