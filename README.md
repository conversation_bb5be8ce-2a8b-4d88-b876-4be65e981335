# Autonomous Task-Based Agent

An intelligent, task-based autonomous agent powered by <PERSON><PERSON><PERSON><PERSON> and Mixtral-8x7B-Instruct. This agent can process documents, answer questions, perform web searches, and maintain conversational context.

## 🚀 Features

- **Document Processing**: Upload and process PDF and TXT files
- **Question Answering**: Ask questions about processed documents
- **Web Search**: Search the internet for current information
- **Conversational Memory**: Maintains context across conversations
- **Modular Design**: Easily extensible with new tools
- **CLI Interface**: Simple command-line interface for interaction

## 📋 Requirements

- Python 3.8+
- Hugging Face API token (for accessing Mixtral model)
- Internet connection (for web search functionality)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd autonomous-agent
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Hugging Face API token:
   ```
   HUGGINGFACE_API_TOKEN=your_token_here
   ```

4. **Get a Hugging Face API Token**:
   - Visit [Hugging Face](https://huggingface.co/)
   - Create an account or log in
   - Go to Settings → Access Tokens
   - Create a new token with read permissions

## 🚀 Quick Start

1. **Run the agent**:
   ```bash
   python main.py
   ```

2. **Basic commands**:
   - `help` - Show available commands
   - `process documents/my_file.pdf` - Process a document
   - `What is the main topic?` - Ask questions about documents
   - `Search for AI news` - Perform web searches
   - `clear` - Clear conversation memory
   - `quit` - Exit the application

## 📖 Usage Examples

### Document Processing and Q&A

```bash
# Process a document
You: process examples/sample_document.pdf

# Ask questions about the document
You: What are the main points discussed in this document?
You: Can you summarize the conclusion section?
You: What does the author say about artificial intelligence?
```

### Web Search

```bash
# General web search
You: What is the latest news about climate change?
You: Search for information about Python programming
You: Find recent developments in machine learning
```

### Mixed Queries

```bash
# The agent automatically chooses the right tool
You: Compare what my document says about AI with current web information
You: How does the information in my PDF relate to recent news?
```

## 🏗️ Architecture

```
autonomous-agent/
├── agent/                  # Core agent implementation
│   ├── __init__.py
│   └── autonomous_agent.py
├── tools/                  # Agent tools
│   ├── __init__.py
│   ├── file_processor.py   # Document processing
│   ├── document_qa.py      # Document Q&A
│   └── web_search.py       # Web search
├── tests/                  # Test suite
├── examples/               # Example documents
├── vector_stores/          # Processed document storage
├── documents/              # Document storage
├── config.py              # Configuration
├── main.py                # CLI interface
└── requirements.txt       # Dependencies
```

## 🔧 Configuration

The agent can be configured through environment variables in the `.env` file:

```bash
# Required
HUGGINGFACE_API_TOKEN=your_token_here

# Optional customizations
LLM_MODEL_NAME=mistralai/Mixtral-8x7B-Instruct-v0.1
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MAX_TOKENS=1000
TEMPERATURE=0.7
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_file_processor.py

# Run with coverage
pytest --cov=agent --cov=tools
```

## 📚 User Stories Validation

✅ **General Knowledge Questions**: Ask the agent any question and it will search the web for answers

✅ **Document Upload and Summary**: Process PDF/TXT files and get summaries

✅ **Document-Specific Questions**: Ask detailed questions about processed documents

✅ **Conversation Memory**: The agent remembers previous interactions in the session

✅ **Transparent Tool Usage**: See which tools the agent uses and its reasoning process

## 🔮 Future Enhancements

- [ ] Web-based GUI interface
- [ ] Support for more document formats (DOCX, HTML, etc.)
- [ ] Multi-document processing and cross-document queries
- [ ] Integration with more LLM providers
- [ ] API endpoint for programmatic access
- [ ] Advanced document analysis (tables, images, etc.)
- [ ] Persistent conversation history
- [ ] Custom tool development framework

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **"HUGGINGFACE_API_TOKEN is required" error**:
   - Ensure you have set the token in your `.env` file
   - Verify the token is valid and has appropriate permissions

2. **Import errors**:
   - Make sure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

3. **Document processing fails**:
   - Ensure the file path is correct
   - Check file permissions
   - Verify file format is supported (PDF, TXT)

4. **Web search not working**:
   - Check internet connection
   - DuckDuckGo search may have rate limits

### Getting Help

- Check the [Issues](https://github.com/your-repo/issues) page
- Review the documentation
- Run with verbose logging for debugging

## 📞 Support

For questions, issues, or contributions, please:
- Open an issue on GitHub
- Check existing documentation
- Review the test cases for usage examples
