#!/usr/bin/env python3
"""
Test runner script for the Autonomous Task-Based Agent.

This script runs all tests and provides a summary of results.
"""

import sys
import os
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False


def main():
    """Run all tests and checks."""
    print("🧪 Autonomous Task-Based Agent - Test Suite")
    print("=" * 60)
    
    # Check if pytest is available
    try:
        import pytest
    except ImportError:
        print("❌ pytest not found. Installing...")
        if not run_command("pip install pytest pytest-cov", "Installing pytest"):
            print("❌ Failed to install pytest")
            return 1
    
    # List of test commands to run
    test_commands = [
        ("python -m pytest tests/ -v", "Running unit tests"),
        ("python -m pytest tests/ --cov=agent --cov=tools --cov-report=term-missing", "Running tests with coverage"),
        ("python -c \"import config; config.Config()\"", "Testing configuration"),
        ("python -c \"from tools import FileProcessor, DocumentQARetriever, WebSearchTool\"", "Testing imports"),
    ]
    
    # Run tests
    results = []
    for command, description in test_commands:
        success = run_command(command, description)
        results.append((description, success))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    failed = 0
    
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {description}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(results)} | Passed: {passed} | Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
