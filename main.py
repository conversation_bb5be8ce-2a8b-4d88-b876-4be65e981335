#!/usr/bin/env python3
"""
Main CLI interface for the Autonomous Task-Based Agent.

This script provides a command-line interface for interacting with the agent.
"""

import sys
import os
import argparse
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.table import Table

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent import AutonomousAgent
from config import Config


class AgentCLI:
    """Command-line interface for the autonomous agent."""
    
    def __init__(self):
        """Initialize the CLI."""
        self.console = Console()
        self.agent = None
        
    def display_welcome(self):
        """Display welcome message."""
        welcome_text = Text()
        welcome_text.append("🤖 Autonomous Task-Based Agent\n", style="bold blue")
        welcome_text.append("Powered by Lang<PERSON>hain and Mixtral-8x7B-Instruct\n\n", style="italic")
        welcome_text.append("Features:\n", style="bold")
        welcome_text.append("• Document processing (PDF, TXT)\n")
        welcome_text.append("• Question answering from documents\n")
        welcome_text.append("• Web search capabilities\n")
        welcome_text.append("• Conversational memory\n\n")
        welcome_text.append("Type 'help' for available commands or 'quit' to exit.", style="dim")
        
        self.console.print(Panel(welcome_text, title="Welcome", border_style="blue"))
    
    def display_help(self):
        """Display help information."""
        table = Table(title="Available Commands")
        table.add_column("Command", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        
        table.add_row("help", "Show this help message")
        table.add_row("quit / exit", "Exit the application")
        table.add_row("clear", "Clear conversation memory")
        table.add_row("history", "Show conversation history")
        table.add_row("tools", "List available tools")
        table.add_row("docs", "List processed documents")
        table.add_row("process <file>", "Process a document file")
        table.add_row("<question>", "Ask any question to the agent")
        
        self.console.print(table)
        
        # Examples
        examples_text = Text()
        examples_text.append("\n💡 Example Commands:\n", style="bold yellow")
        examples_text.append("• process documents/my_document.pdf\n")
        examples_text.append("• What is the main topic of the document?\n")
        examples_text.append("• Search for information about artificial intelligence\n")
        examples_text.append("• Summarize the key points from the uploaded file\n")
        
        self.console.print(Panel(examples_text, title="Examples", border_style="yellow"))
    
    def initialize_agent(self):
        """Initialize the agent with error handling."""
        try:
            self.console.print("🔄 Initializing agent...", style="yellow")
            self.agent = AutonomousAgent()
            return True
        except ValueError as e:
            self.console.print(Panel(
                Text(str(e), style="bold red"),
                title="Configuration Error"
            ))
            self.console.print("\n💡 Please check your .env file and ensure HUGGINGFACE_API_TOKEN is set.")
            return False
        except Exception as e:
            self.console.print(Panel(
                Text(f"Failed to initialize agent: {str(e)}", style="bold red"),
                title="Initialization Error"
            ))
            return False
    
    def handle_command(self, user_input: str) -> bool:
        """
        Handle user commands.
        
        Args:
            user_input: User's input
            
        Returns:
            True to continue, False to exit
        """
        command = user_input.strip().lower()
        
        if command in ['quit', 'exit']:
            return False
        elif command == 'help':
            self.display_help()
        elif command == 'clear':
            self.agent.clear_memory()
        elif command == 'history':
            self.show_history()
        elif command == 'tools':
            self.show_tools()
        elif command == 'docs':
            self.show_documents()
        elif command.startswith('process '):
            file_path = command[8:].strip()
            self.process_file(file_path)
        else:
            # Regular question/command for the agent
            response = self.agent.chat(user_input)
        
        return True
    
    def show_history(self):
        """Show conversation history."""
        history = self.agent.get_conversation_history()
        if not history:
            self.console.print("📝 No conversation history yet.", style="yellow")
            return
        
        self.console.print(Panel(
            Text(f"Conversation has {len(history)} messages", style="bold"),
            title="Conversation History"
        ))
        
        for i, message in enumerate(history[-10:], 1):  # Show last 10 messages
            role = "User" if message.type == "human" else "Agent"
            content = message.content[:100] + "..." if len(message.content) > 100 else message.content
            self.console.print(f"{i}. {role}: {content}")
    
    def show_tools(self):
        """Show available tools."""
        tools = self.agent.list_available_tools()
        
        table = Table(title="Available Tools")
        table.add_column("Tool", style="cyan")
        table.add_column("Description", style="white")
        
        descriptions = {
            "file_processor": "Process and index local files (PDF, TXT)",
            "document_qa": "Answer questions based on processed documents",
            "web_search": "Search the web for information"
        }
        
        for tool in tools:
            desc = descriptions.get(tool, "No description available")
            table.add_row(tool, desc)
        
        self.console.print(table)
    
    def show_documents(self):
        """Show processed documents."""
        from tools.document_qa import DocumentQARetriever
        doc_qa = DocumentQARetriever()
        docs_info = doc_qa.list_processed_documents()
        
        self.console.print(Panel(
            Text(docs_info, style="white"),
            title="Processed Documents"
        ))
    
    def process_file(self, file_path: str):
        """Process a file directly."""
        if not file_path:
            self.console.print("❌ Please provide a file path.", style="red")
            return
        
        # Check if file exists
        if not os.path.exists(file_path):
            self.console.print(f"❌ File not found: {file_path}", style="red")
            return
        
        # Process through the agent
        response = self.agent.chat(f"Please process the file: {file_path}")
    
    def run(self):
        """Run the CLI interface."""
        self.display_welcome()
        
        # Initialize agent
        if not self.initialize_agent():
            return
        
        # Main interaction loop
        try:
            while True:
                try:
                    user_input = Prompt.ask("\n[bold blue]You[/bold blue]")
                    
                    if not user_input.strip():
                        continue
                    
                    if not self.handle_command(user_input):
                        break
                        
                except KeyboardInterrupt:
                    if Confirm.ask("\n\nDo you want to exit?"):
                        break
                    else:
                        continue
                except EOFError:
                    break
                    
        except Exception as e:
            self.console.print(f"\n❌ An error occurred: {str(e)}", style="red")
        
        self.console.print("\n👋 Goodbye!", style="bold blue")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Autonomous Task-Based Agent CLI")
    parser.add_argument("--version", action="version", version="1.0.0")
    parser.add_argument("--config", help="Path to configuration file")
    
    args = parser.parse_args()
    
    # Initialize and run CLI
    cli = AgentCLI()
    cli.run()


if __name__ == "__main__":
    main()
