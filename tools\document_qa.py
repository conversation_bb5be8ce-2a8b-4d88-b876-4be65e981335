"""Document QA tool for answering questions based on processed documents."""

import os
import pickle
from typing import List, Optional, Dict, Any
from pathlib import Path

from langchain.vectorstores import FAISS
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.schema import Document

from config import Config


class DocumentQARetriever:
    """Tool for answering questions based on processed documents."""
    
    def __init__(self):
        """Initialize the document QA retriever."""
        self.config = Config()
        self.embeddings = HuggingFaceEmbeddings(
            model_name=self.config.EMBEDDING_MODEL_NAME
        )
        self._loaded_stores = {}  # Cache for loaded vector stores
    
    def query_documents(self, question: str, top_k: int = 3) -> str:
        """
        Answer a question based on processed documents.
        
        Args:
            question: The question to answer
            top_k: Number of relevant chunks to retrieve
            
        Returns:
            Answer based on document content or error message
        """
        try:
            # Get all available vector stores
            vector_stores = self._get_available_vector_stores()
            
            if not vector_stores:
                return "No documents have been processed yet. Please process a document first using the file_processor tool."
            
            # Search across all vector stores
            all_results = []
            for store_name, store_path in vector_stores.items():
                try:
                    vector_store = self._load_vector_store(store_path)
                    if vector_store:
                        # Perform similarity search
                        results = vector_store.similarity_search_with_score(question, k=top_k)
                        for doc, score in results:
                            all_results.append({
                                "content": doc.page_content,
                                "metadata": doc.metadata,
                                "score": score,
                                "store": store_name
                            })
                except Exception as e:
                    continue  # Skip problematic stores
            
            if not all_results:
                return "No relevant information found in the processed documents."
            
            # Sort by relevance score (lower is better for FAISS)
            all_results.sort(key=lambda x: x["score"])
            
            # Take top results
            top_results = all_results[:top_k]
            
            # Format the response
            response = self._format_response(question, top_results)
            
            return response
            
        except Exception as e:
            return f"Error querying documents: {str(e)}"
    
    def _get_available_vector_stores(self) -> Dict[str, str]:
        """Get all available vector stores."""
        vector_stores = {}
        
        if not os.path.exists(self.config.VECTOR_STORE_PATH):
            return vector_stores
        
        for item in os.listdir(self.config.VECTOR_STORE_PATH):
            item_path = os.path.join(self.config.VECTOR_STORE_PATH, item)
            
            # Check if it's a vector store directory
            if os.path.isdir(item_path) and item.endswith("_vectorstore"):
                # Check if it has the required FAISS files
                index_file = os.path.join(item_path, "index.faiss")
                pkl_file = os.path.join(item_path, "index.pkl")
                
                if os.path.exists(index_file) and os.path.exists(pkl_file):
                    store_name = item.replace("_vectorstore", "")
                    vector_stores[store_name] = item_path
        
        return vector_stores
    
    def _load_vector_store(self, store_path: str) -> Optional[FAISS]:
        """Load a vector store from disk."""
        try:
            # Check cache first
            if store_path in self._loaded_stores:
                return self._loaded_stores[store_path]
            
            # Load from disk
            vector_store = FAISS.load_local(
                store_path, 
                self.embeddings,
                allow_dangerous_deserialization=True
            )
            
            # Cache the loaded store
            self._loaded_stores[store_path] = vector_store
            
            return vector_store
            
        except Exception as e:
            print(f"Error loading vector store from {store_path}: {str(e)}")
            return None
    
    def _format_response(self, question: str, results: List[Dict[str, Any]]) -> str:
        """Format the response based on retrieved documents."""
        if not results:
            return "No relevant information found."
        
        # Create a comprehensive answer
        response_parts = []
        response_parts.append(f"Based on the processed documents, here's what I found regarding your question: '{question}'\n")
        
        # Group results by source
        sources = {}
        for result in results:
            source = result["metadata"].get("source", "Unknown")
            if source not in sources:
                sources[source] = []
            sources[source].append(result)
        
        # Format information by source
        for source, source_results in sources.items():
            source_name = Path(source).name if source != "Unknown" else "Unknown"
            response_parts.append(f"\n📄 From {source_name}:")
            
            for i, result in enumerate(source_results, 1):
                content = result["content"].strip()
                # Truncate very long content
                if len(content) > 300:
                    content = content[:300] + "..."
                
                response_parts.append(f"\n{i}. {content}")
        
        # Add source summary
        if len(sources) > 1:
            source_names = [Path(s).name for s in sources.keys() if s != "Unknown"]
            response_parts.append(f"\n\n📚 Information retrieved from {len(sources)} document(s): {', '.join(source_names)}")
        
        return "".join(response_parts)
    
    def list_processed_documents(self) -> str:
        """List all processed documents."""
        vector_stores = self._get_available_vector_stores()
        
        if not vector_stores:
            return "No documents have been processed yet."
        
        response = "📚 Processed Documents:\n"
        for i, (store_name, store_path) in enumerate(vector_stores.items(), 1):
            # Try to get metadata
            metadata_path = f"{store_path}_metadata.pkl"
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'rb') as f:
                        metadata = pickle.load(f)
                        num_chunks = metadata.get("num_documents", "Unknown")
                        sources = metadata.get("sources", [])
                        if sources:
                            source_name = Path(sources[0]).name
                            response += f"{i}. {source_name} ({num_chunks} chunks)\n"
                        else:
                            response += f"{i}. {store_name} ({num_chunks} chunks)\n"
                except:
                    response += f"{i}. {store_name}\n"
            else:
                response += f"{i}. {store_name}\n"
        
        return response
    
    def clear_cache(self):
        """Clear the vector store cache."""
        self._loaded_stores.clear()
